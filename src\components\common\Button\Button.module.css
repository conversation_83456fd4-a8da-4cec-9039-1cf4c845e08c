.button {
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-family: inherit;
  font-weight: 500;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Variants */
.primary {
  background-color: var(--color-primary);
  color: var(--color-white);
}

.primary:hover:not(:disabled) {
  background-color: var(--color-primary-dark);
}

.secondary {
  background-color: var(--color-gray-100);
  color: var(--color-gray-900);
}

.secondary:hover:not(:disabled) {
  background-color: var(--color-gray-200);
}

.outline {
  background-color: transparent;
  border: 1px solid var(--color-gray-300);
  color: var(--color-gray-700);
}

.outline:hover:not(:disabled) {
  background-color: var(--color-gray-50);
}

/* Sizes */
.small {
  padding: 8px 12px;
  font-size: 14px;
  line-height: 20px;
}

.medium {
  padding: 10px 16px;
  font-size: 16px;
  line-height: 24px;
}

.large {
  padding: 12px 20px;
  font-size: 18px;
  line-height: 28px;
}
