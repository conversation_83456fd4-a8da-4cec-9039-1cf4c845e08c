import React from 'react';
import styles from './Header.module.css';
import { Bell } from '../../icons/Bell';
import { UserCircle } from '../../icons/UserCircle';
import { Cog6Tooth } from '../../icons/C og6Tooth';

interface HeaderProps {
  title?: string;
}

export const Header: React.FC<HeaderProps> = ({ title = 'Customer Support' }) => {
  return (
    <header className={styles.header}>
      <div className={styles.titleSection}>
        <h1 className={styles.title}>{title}</h1>
      </div>
      
      <div className={styles.actions}>
        <button className={styles.iconButton} title="Notifications">
          <Bell />
        </button>
        <button className={styles.iconButton} title="Settings">
          <Cog6Tooth />
        </button>
        <button className={styles.profileButton} title="Profile">
          <UserCircle />
          <span className={styles.profileText}><PERSON></span>
        </button>
      </div>
    </header>
  );
};
