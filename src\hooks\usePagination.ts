import { useTicketStore } from '../store/useTicketStore';

export const usePagination = () => {
  const {
    currentPage,
    totalPages,
    pageSize,
    setCurrentPage,
  } = useTicketStore();

  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const goToFirstPage = () => {
    setCurrentPage(1);
  };

  const goToLastPage = () => {
    setCurrentPage(totalPages);
  };

  const canGoNext = currentPage < totalPages;
  const canGoPrevious = currentPage > 1;

  const getPageNumbers = (maxVisible: number = 5) => {
    const pages: number[] = [];
    const halfVisible = Math.floor(maxVisible / 2);
    
    let start = Math.max(1, currentPage - halfVisible);
    let end = Math.min(totalPages, start + maxVisible - 1);
    
    if (end - start + 1 < maxVisible) {
      start = Math.max(1, end - maxVisible + 1);
    }
    
    for (let i = start; i <= end; i++) {
      pages.push(i);
    }
    
    return pages;
  };

  const getPageInfo = () => {
    const startItem = (currentPage - 1) * pageSize + 1;
    const endItem = Math.min(currentPage * pageSize, totalPages * pageSize);
    const totalItems = totalPages * pageSize;
    
    return {
      startItem,
      endItem,
      totalItems,
      currentPage,
      totalPages,
      pageSize,
    };
  };

  return {
    currentPage,
    totalPages,
    pageSize,
    canGoNext,
    canGoPrevious,
    goToPage,
    goToNextPage,
    goToPreviousPage,
    goToFirstPage,
    goToLastPage,
    getPageNumbers,
    getPageInfo,
  };
};
