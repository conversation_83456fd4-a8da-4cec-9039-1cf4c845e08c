import React from 'react';

interface IconProps {
  width?: string | number;
  height?: string | number;
  className?: string;
}

export const Identification: React.FC<IconProps> = ({ 
  width = 20, 
  height = 20, 
  className = '' 
}) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={1.5}
    className={className}
  >
    <path strokeLinecap="round" strokeLinejoin="round" d="M15 9h3.75M15 12h3.75M15 15h3.75M4.5 19.5h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5ZM12.75 9a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM8.25 12.75a3 3 0 1 1 6 0 3 3 0 0 1-6 0Z" />
  </svg>
);
