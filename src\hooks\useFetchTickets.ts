import { useEffect } from 'react';
import { useTicketStore } from '../store/useTicketStore';
import { ticketsApi } from '../services/ticketsApi';

interface UseFetchTicketsOptions {
  autoFetch?: boolean;
  refetchInterval?: number;
}

export const useFetchTickets = (options: UseFetchTicketsOptions = {}) => {
  const { autoFetch = true, refetchInterval } = options;
  
  const {
    tickets,
    loading,
    error,
    currentPage,
    pageSize,
    searchQuery,
    statusFilter,
    priorityFilter,
    setTickets,
    setLoading,
    setError,
    setTotalPages,
  } = useTicketStore();

  const fetchTickets = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = {
        page: currentPage,
        limit: pageSize,
        search: searchQuery,
        status: statusFilter,
        priority: priorityFilter,
      };
      
      const response = await ticketsApi.getTickets(params);
      
      setTickets(response.data);
      setTotalPages(response.pagination.totalPages);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch tickets';
      setError(errorMessage);
      console.error('Error fetching tickets:', err);
    } finally {
      setLoading(false);
    }
  };

  const refetch = () => {
    fetchTickets();
  };

  useEffect(() => {
    if (autoFetch) {
      fetchTickets();
    }
  }, [currentPage, searchQuery, statusFilter, priorityFilter]);

  useEffect(() => {
    if (refetchInterval && refetchInterval > 0) {
      const interval = setInterval(fetchTickets, refetchInterval);
      return () => clearInterval(interval);
    }
  }, [refetchInterval]);

  return {
    tickets,
    loading,
    error,
    refetch,
  };
};
