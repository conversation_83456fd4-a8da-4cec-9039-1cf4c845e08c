.sidebar {
  width: 240px;
  height: 100vh;
  background-color: var(--color-white);
  border-right: 1px solid var(--color-gray-200);
  display: flex;
  flex-direction: column;
  padding: 24px 0;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 0 24px;
  margin-bottom: 32px;
}

.logoIcon {
  width: 32px;
  height: 32px;
  background-color: var(--color-primary);
  color: var(--color-white);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.logoText {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-gray-900);
}

.navigation {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 0 16px;
}

.navItem {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  background-color: transparent;
  color: var(--color-gray-700);
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
}

.navItem:hover {
  background-color: var(--color-gray-50);
  color: var(--color-gray-900);
}

.navItem.active {
  background-color: var(--color-primary-light);
  color: var(--color-primary);
}

.navIcon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navLabel {
  font-size: 14px;
  font-weight: 500;
}
