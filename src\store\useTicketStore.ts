import { create } from 'zustand';

export interface Ticket {
  id: string;
  subject: string;
  customer: string;
  status: 'open' | 'pending' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assignee: string;
  createdAt: string;
  updatedAt: string;
  unreadCount?: number;
}

export interface Message {
  id: string;
  content: string;
  sender: {
    name: string;
    type: 'customer' | 'agent';
    avatar?: string;
  };
  timestamp: string;
  attachments?: Array<{
    id: string;
    name: string;
    url: string;
    type: string;
  }>;
}

export interface Customer {
  name: string;
  email: string;
  phone?: string;
  location?: string;
  avatar?: string;
}

interface TicketStore {
  // State
  tickets: Ticket[];
  selectedTicket: Ticket | null;
  messages: Message[];
  loading: boolean;
  error: string | null;
  
  // Pagination
  currentPage: number;
  totalPages: number;
  pageSize: number;
  
  // Search and filters
  searchQuery: string;
  statusFilter: string;
  priorityFilter: string;
  
  // Actions
  setTickets: (tickets: Ticket[]) => void;
  setSelectedTicket: (ticket: Ticket | null) => void;
  setMessages: (messages: Message[]) => void;
  addMessage: (message: Message) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Pagination actions
  setCurrentPage: (page: number) => void;
  setTotalPages: (pages: number) => void;
  
  // Search and filter actions
  setSearchQuery: (query: string) => void;
  setStatusFilter: (status: string) => void;
  setPriorityFilter: (priority: string) => void;
  
  // Reset actions
  reset: () => void;
}

const initialState = {
  tickets: [],
  selectedTicket: null,
  messages: [],
  loading: false,
  error: null,
  currentPage: 1,
  totalPages: 1,
  pageSize: 20,
  searchQuery: '',
  statusFilter: '',
  priorityFilter: '',
};

export const useTicketStore = create<TicketStore>((set, get) => ({
  ...initialState,
  
  setTickets: (tickets) => set({ tickets }),
  setSelectedTicket: (ticket) => set({ selectedTicket: ticket }),
  setMessages: (messages) => set({ messages }),
  addMessage: (message) => set((state) => ({ 
    messages: [...state.messages, message] 
  })),
  setLoading: (loading) => set({ loading }),
  setError: (error) => set({ error }),
  
  setCurrentPage: (page) => set({ currentPage: page }),
  setTotalPages: (pages) => set({ totalPages: pages }),
  
  setSearchQuery: (query) => set({ searchQuery: query, currentPage: 1 }),
  setStatusFilter: (status) => set({ statusFilter: status, currentPage: 1 }),
  setPriorityFilter: (priority) => set({ priorityFilter: priority, currentPage: 1 }),
  
  reset: () => set(initialState),
}));
