import React, { useState } from 'react';
import styles from './ReplyForm.module.css';
import { Button } from '../../common/Button/Button';
import { PaperClip } from '../../icons/PaperClip';
import { FaceSmile } from '../../icons/FaceSmile';

interface ReplyFormProps {
  onSendMessage: (content: string, attachments?: File[]) => void;
  loading?: boolean;
  placeholder?: string;
}

export const ReplyForm: React.FC<ReplyFormProps> = ({
  onSendMessage,
  loading = false,
  placeholder = 'Type your reply...',
}) => {
  const [message, setMessage] = useState('');
  const [attachments, setAttachments] = useState<File[]>([]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim() || attachments.length > 0) {
      onSendMessage(message.trim(), attachments);
      setMessage('');
      setAttachments([]);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      handleSubmit(e);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setAttachments(prev => [...prev, ...files]);
    e.target.value = ''; // Reset input
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <form className={styles.replyForm} onSubmit={handleSubmit}>
      {attachments.length > 0 && (
        <div className={styles.attachments}>
          {attachments.map((file, index) => (
            <div key={index} className={styles.attachment}>
              <PaperClip />
              <span className={styles.fileName}>{file.name}</span>
              <span className={styles.fileSize}>({formatFileSize(file.size)})</span>
              <button
                type="button"
                className={styles.removeAttachment}
                onClick={() => removeAttachment(index)}
              >
                ×
              </button>
            </div>
          ))}
        </div>
      )}

      <div className={styles.inputContainer}>
        <textarea
          className={styles.messageInput}
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          rows={3}
          disabled={loading}
        />
        
        <div className={styles.toolbar}>
          <div className={styles.toolbarLeft}>
            <label className={styles.attachButton}>
              <PaperClip />
              <input
                type="file"
                multiple
                onChange={handleFileSelect}
                className={styles.fileInput}
                disabled={loading}
              />
            </label>
            
            <button
              type="button"
              className={styles.emojiButton}
              disabled={loading}
              title="Add emoji"
            >
              <FaceSmile />
            </button>
          </div>
          
          <div className={styles.toolbarRight}>
            <span className={styles.shortcut}>Ctrl+Enter to send</span>
            <Button
              type="submit"
              disabled={loading || (!message.trim() && attachments.length === 0)}
              size="small"
            >
              {loading ? 'Sending...' : 'Send'}
            </Button>
          </div>
        </div>
      </div>
    </form>
  );
};
