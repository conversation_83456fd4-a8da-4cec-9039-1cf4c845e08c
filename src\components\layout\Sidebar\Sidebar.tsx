import React from 'react';
import styles from './Sidebar.module.css';
import { Ticket } from '../../icons/Ticket';
import { UserGroup } from '../../icons/UserGroup';
import { DocumentChartBar } from '../../icons/DocumentChartBar';
import { Squares2x2 } from '../../icons/Squares2x2';

interface SidebarItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  active?: boolean;
}

interface SidebarProps {
  activeItem?: string;
  onItemClick?: (itemId: string) => void;
}

export const Sidebar: React.FC<SidebarProps> = ({
  activeItem = 'tickets',
  onItemClick,
}) => {
  const sidebarItems: SidebarItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: <Squares2x2 />,
      active: activeItem === 'dashboard',
    },
    {
      id: 'tickets',
      label: 'Tickets',
      icon: <Ticket />,
      active: activeItem === 'tickets',
    },
    {
      id: 'customers',
      label: 'Customers',
      icon: <UserGroup />,
      active: activeItem === 'customers',
    },
    {
      id: 'reports',
      label: 'Reports',
      icon: <DocumentChartBar />,
      active: activeItem === 'reports',
    },
  ];

  return (
    <aside className={styles.sidebar}>
      <div className={styles.logo}>
        <div className={styles.logoIcon}>CS</div>
        <span className={styles.logoText}>Support</span>
      </div>
      
      <nav className={styles.navigation}>
        {sidebarItems.map((item) => (
          <button
            key={item.id}
            className={`${styles.navItem} ${item.active ? styles.active : ''}`}
            onClick={() => onItemClick?.(item.id)}
          >
            <span className={styles.navIcon}>{item.icon}</span>
            <span className={styles.navLabel}>{item.label}</span>
          </button>
        ))}
      </nav>
    </aside>
  );
};
