.replyForm {
  background-color: var(--color-white);
  border-top: 1px solid var(--color-gray-200);
  padding: 16px 24px;
}

.attachments {
  margin-bottom: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.attachment {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  background-color: var(--color-gray-100);
  border-radius: 6px;
  font-size: 12px;
  color: var(--color-gray-700);
}

.attachment svg {
  width: 14px;
  height: 14px;
  color: var(--color-gray-500);
}

.fileName {
  font-weight: 500;
}

.fileSize {
  color: var(--color-gray-500);
}

.removeAttachment {
  background: none;
  border: none;
  color: var(--color-gray-500);
  cursor: pointer;
  font-size: 16px;
  line-height: 1;
  padding: 0;
  margin-left: 4px;
}

.removeAttachment:hover {
  color: var(--color-red-500);
}

.inputContainer {
  border: 1px solid var(--color-gray-300);
  border-radius: 8px;
  background-color: var(--color-white);
  overflow: hidden;
}

.messageInput {
  width: 100%;
  border: none;
  outline: none;
  padding: 12px 16px;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  background-color: transparent;
}

.messageInput::placeholder {
  color: var(--color-gray-500);
}

.messageInput:disabled {
  background-color: var(--color-gray-50);
  color: var(--color-gray-500);
}

.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  border-top: 1px solid var(--color-gray-200);
  background-color: var(--color-gray-50);
}

.toolbarLeft {
  display: flex;
  align-items: center;
  gap: 8px;
}

.attachButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background-color: transparent;
  color: var(--color-gray-600);
  cursor: pointer;
  transition: all 0.2s ease;
}

.attachButton:hover {
  background-color: var(--color-gray-200);
  color: var(--color-gray-900);
}

.fileInput {
  display: none;
}

.emojiButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background-color: transparent;
  color: var(--color-gray-600);
  cursor: pointer;
  transition: all 0.2s ease;
}

.emojiButton:hover:not(:disabled) {
  background-color: var(--color-gray-200);
  color: var(--color-gray-900);
}

.emojiButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbarRight {
  display: flex;
  align-items: center;
  gap: 12px;
}

.shortcut {
  font-size: 12px;
  color: var(--color-gray-500);
}
