import React from 'react';
import { 
  TypographyProps, 
  getTypographyClassName, 
  getTypographyElement,
  getTypographyStyles 
} from '../../../hooks/useTypography';

/**
 * Typography component that uses Figma design tokens
 * 
 * Usage:
 * <Typography variant="heading1">Main Title</Typography>
 * <Typography variant="bodyTextNormal">Regular text content</Typography>
 * <Typography variant="bodyTextSmall" as="span">Small text</Typography>
 */
export const Typography: React.FC<TypographyProps> = ({
  variant = 'bodyTextNormal',
  children,
  className = '',
  style = {},
  as,
}) => {
  const Element = as || getTypographyElement(variant);
  const typographyClassName = getTypographyClassName(variant);
  const typographyStyles = getTypographyStyles(variant);
  
  // Combine Figma styles with any custom styles passed in
  const combinedStyles = {
    ...typographyStyles,
    ...style,
    // Remove background from Figma tokens as it's not needed for text
    background: undefined,
  };
  
  return (
    <Element 
      className={`${typographyClassName} ${className}`.trim()}
      style={combinedStyles}
    >
      {children}
    </Element>
  );
};
