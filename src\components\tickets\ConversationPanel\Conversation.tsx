import React from 'react';
import styles from './Conversation.module.css';
import { MessageBubble } from './MessageBubble';
import { User } from '../../icons/User';
import { Phone } from '../../icons/Phone';
import { Envelope } from '../../icons/Envelope';
import { MapPin } from '../../icons/MapPin';

interface Message {
  id: string;
  content: string;
  sender: {
    name: string;
    type: 'customer' | 'agent';
    avatar?: string;
  };
  timestamp: string;
  attachments?: Array<{
    id: string;
    name: string;
    url: string;
    type: string;
  }>;
}

interface Customer {
  name: string;
  email: string;
  phone?: string;
  location?: string;
  avatar?: string;
}

interface Ticket {
  id: string;
  subject: string;
  status: 'open' | 'pending' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: string;
  customer: Customer;
}

interface ConversationProps {
  ticket: Ticket;
  messages: Message[];
  loading?: boolean;
}

export const Conversation: React.FC<ConversationProps> = ({
  ticket,
  messages,
  loading = false,
}) => {
  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.spinner}>Loading conversation...</div>
      </div>
    );
  }

  return (
    <div className={styles.conversation}>
      <div className={styles.header}>
        <div className={styles.ticketInfo}>
          <h2 className={styles.subject}>{ticket.subject}</h2>
          <div className={styles.metadata}>
            <span className={`${styles.status} ${styles[ticket.status]}`}>
              {ticket.status.toUpperCase()}
            </span>
            <span className={`${styles.priority} ${styles[ticket.priority]}`}>
              {ticket.priority.toUpperCase()}
            </span>
            <span className={styles.ticketId}>#{ticket.id}</span>
          </div>
        </div>
        
        <div className={styles.customerInfo}>
          <div className={styles.customerHeader}>
            <div className={styles.avatar}>
              {ticket.customer.avatar ? (
                <img src={ticket.customer.avatar} alt={ticket.customer.name} />
              ) : (
                <User />
              )}
            </div>
            <div className={styles.customerDetails}>
              <h3 className={styles.customerName}>{ticket.customer.name}</h3>
              <div className={styles.contactInfo}>
                <div className={styles.contactItem}>
                  <Envelope />
                  <span>{ticket.customer.email}</span>
                </div>
                {ticket.customer.phone && (
                  <div className={styles.contactItem}>
                    <Phone />
                    <span>{ticket.customer.phone}</span>
                  </div>
                )}
                {ticket.customer.location && (
                  <div className={styles.contactItem}>
                    <MapPin />
                    <span>{ticket.customer.location}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className={styles.messagesContainer}>
        {messages.map((message) => (
          <MessageBubble key={message.id} message={message} />
        ))}
      </div>
    </div>
  );
};
