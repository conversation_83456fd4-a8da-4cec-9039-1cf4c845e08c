/* Import DM Sans font */
@import url("https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap");

/* CSS Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
}

html {
  height: 100%;
}

body {
  height: 100%;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: "DM Sans", -apple-system, BlinkMacSystemFont, "Segoe UI",
    "Roboto", sans-serif;
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
}

#root {
  height: 100%;
}

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

input,
button,
textarea,
select {
  font: inherit;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

/* CSS Custom Properties (Design Tokens) */
:root {
  /* Brand Colors */
  --color-deep-purple: #3a2f53;
  --color-dark-purple: #251f32;
  --color-almost-black: #252526;
  --color-grey: #6c6c6c;
  --color-purple: #7336e6;
  --color-light-purple: #d2c0f5;
  --color-blue: #2c9ed8;
  --color-light-blue: #caedff;
  --color-green-yellow: #bfd445;
  --color-red: #e76a63;
  --color-pale-red: #fdc2bf;
  --color-light-grey: #f0f0f0;
  --color-off-white: #f9f5ff;
  --color-white: #ffffff;

  /* Light Theme (Default) */
  --color-bg-primary: var(--color-off-white);
  --color-bg-secondary: var(--color-white);
  --color-bg-tertiary: var(--color-light-grey);
  --color-text-primary: var(--color-almost-black);
  --color-text-secondary: var(--color-grey);
  --color-accent-primary: var(--color-purple);
  --color-accent-secondary: var(--color-light-purple);
  --color-status-open: var(--color-blue);
  --color-status-pending: var(--color-green-yellow);
  --color-status-closed: var(--color-red);
  --color-border: var(--color-light-purple);
  --shadow-primary: 0px 2px 6px rgba(226, 223, 233, 1);
  --shadow-secondary: 0px 2px 6px rgba(185, 181, 192, 0.65);

  /* Legacy color mappings for existing components */
  --color-primary: var(--color-purple);
  --color-primary-light: var(--color-light-purple);
  --color-primary-dark: var(--color-deep-purple);
  --color-gray-50: var(--color-off-white);
  --color-gray-100: var(--color-light-grey);
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: var(--color-grey);
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: var(--color-almost-black);

  /* Status colors for components */
  --color-blue-100: var(--color-light-blue);
  --color-blue-500: var(--color-blue);
  --color-blue-800: #1e40af;

  --color-green-100: #dcfce7;
  --color-green-500: var(--color-green-yellow);
  --color-green-800: #166534;

  --color-yellow-100: #fef3c7;
  --color-yellow-500: var(--color-green-yellow);
  --color-yellow-800: #92400e;

  --color-red-100: var(--color-pale-red);
  --color-red-500: var(--color-red);
  --color-red-light: var(--color-pale-red);
  --color-red-700: #b91c1c;

  --color-orange-100: #ffedd5;
  --color-orange-500: #f97316;
  --color-orange-700: #c2410c;

  /* Typography - Figma Design Tokens */
  --font-family-primary: "DM Sans", sans-serif;
  --font-family-mono: ui-monospace, SFMono-Regular, "SF Mono", Consolas,
    "Liberation Mono", Menlo, monospace;

  /* Heading 1 */
  --heading1-font-family: "DM Sans", sans-serif;
  --heading1-font-weight: 600;
  --heading1-font-size: 1.25rem; /* 20px */
  --heading1-line-height: 100%; /* 20px */
  --heading1-letter-spacing: 0px;
  --heading1-margin: 1rem 0;

  /* Heading 2 */
  --heading2-font-family: "DM Sans", sans-serif;
  --heading2-font-weight: 600;
  --heading2-font-size: 0.875rem; /* 14px */
  --heading2-line-height: 100%; /* 14px */
  --heading2-letter-spacing: 0px;
  --heading2-margin: 0.75rem 0;

  /* Body Text Normal */
  --body-normal-font-family: "DM Sans", sans-serif;
  --body-normal-font-weight: 400;
  --body-normal-font-size: 0.875rem; /* 14px */
  --body-normal-line-height: 100%; /* 14px */
  --body-normal-letter-spacing: 0px;
  --body-normal-margin: 0.75rem 0;

  /* Body Text Small */
  --body-small-font-family: "DM Sans", sans-serif;
  --body-small-font-weight: 400;
  --body-small-font-size: 0.75rem; /* 12px */
  --body-small-line-height: 100%; /* 12px */
  --body-small-letter-spacing: 0px;
  --body-small-margin: 0.5rem 0;

  /* Legacy font sizes for existing components */
  --font-size-xs: 0.75rem; /* 12px */
  --font-size-sm: 0.875rem; /* 14px */
  --font-size-base: 1rem; /* 16px */
  --font-size-lg: 1.125rem; /* 18px */
  --font-size-xl: 1.25rem; /* 20px */
  --font-size-2xl: 1.5rem; /* 24px */
  --font-size-3xl: 1.875rem; /* 30px */
  --font-size-4xl: 2.25rem; /* 36px */

  /* Font Weights */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Line Heights */
  --line-height-tight: 1;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;

  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;

  /* Border Radius */
  --border-radius-sm: 4px;
  --border-radius-md: 6px;
  --border-radius-lg: 8px;
  --border-radius-xl: 12px;
  --border-radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-normal: 200ms ease;
  --transition-slow: 300ms ease;
}

/* Typography Utility Classes */
.heading1 {
  font-family: var(--heading1-font-family);
  font-weight: var(--heading1-font-weight);
  font-size: var(--heading1-font-size);
  line-height: var(--heading1-line-height);
  letter-spacing: var(--heading1-letter-spacing);
  margin: var(--heading1-margin);
  color: var(--color-text-primary);
}

.heading2 {
  font-family: var(--heading2-font-family);
  font-weight: var(--heading2-font-weight);
  font-size: var(--heading2-font-size);
  line-height: var(--heading2-line-height);
  letter-spacing: var(--heading2-letter-spacing);
  margin: var(--heading2-margin);
  color: var(--color-text-primary);
}

.body-normal {
  font-family: var(--body-normal-font-family);
  font-weight: var(--body-normal-font-weight);
  font-size: var(--body-normal-font-size);
  line-height: var(--body-normal-line-height);
  letter-spacing: var(--body-normal-letter-spacing);
  margin: var(--body-normal-margin);
  color: var(--color-text-primary);
}

.body-small {
  font-family: var(--body-small-font-family);
  font-weight: var(--body-small-font-weight);
  font-size: var(--body-small-font-size);
  line-height: var(--body-small-line-height);
  letter-spacing: var(--body-small-letter-spacing);
  margin: var(--body-small-margin);
  color: var(--color-text-secondary);
}

/* General Utility Classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Focus styles */
*:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

*:focus:not(:focus-visible) {
  outline: none;
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--color-gray-300);
  border-radius: var(--border-radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-400);
}

/* Print styles */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
}
