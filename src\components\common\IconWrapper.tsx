import React from 'react';

interface IconWrapperProps {
  children: React.ReactNode;
  size?: 'small' | 'medium' | 'large';
  color?: string;
  className?: string;
}

export const IconWrapper: React.FC<IconWrapperProps> = ({
  children,
  size = 'medium',
  color,
  className = '',
}) => {
  const sizeMap = {
    small: '16px',
    medium: '20px',
    large: '24px',
  };

  const iconSize = sizeMap[size];

  return (
    <span
      className={className}
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: iconSize,
        height: iconSize,
        color: color || 'currentColor',
      }}
    >
      {React.cloneElement(children as React.ReactElement, {
        width: iconSize,
        height: iconSize,
      })}
    </span>
  );
};
