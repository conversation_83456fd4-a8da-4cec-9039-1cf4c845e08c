.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background-color: var(--color-white);
  border-bottom: 1px solid var(--color-gray-200);
  height: 64px;
}

.titleSection {
  display: flex;
  align-items: center;
}

.title {
  font-size: 24px;
  font-weight: 600;
  color: var(--color-gray-900);
  margin: 0;
}

.actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.iconButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 8px;
  background-color: transparent;
  color: var(--color-gray-600);
  cursor: pointer;
  transition: all 0.2s ease;
}

.iconButton:hover {
  background-color: var(--color-gray-100);
  color: var(--color-gray-900);
}

.profileButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: none;
  border-radius: 8px;
  background-color: transparent;
  color: var(--color-gray-700);
  cursor: pointer;
  transition: all 0.2s ease;
}

.profileButton:hover {
  background-color: var(--color-gray-100);
}

.profileText {
  font-size: 14px;
  font-weight: 500;
}
