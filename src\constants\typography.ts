export const typography = {
  heading1: {
    style: {
      fontFamily: "'DM Sans', sans-serif",
      fontWeight: 600,
      fontSize: "1.25rem",    // 20px
      lineHeight: "100%",     // 20px
      letterSpacing: "0px",
      background: "#000000",
    },
    layout: {
      margin: "1rem 0",
    },
  },
  heading2: {
    style: {
      fontFamily: "'DM Sans', sans-serif",
      fontWeight: 600,
      fontSize: "0.875rem",   // 14px
      lineHeight: "100%",     // 14px
      letterSpacing: "0px",
      background: "#000000",
    },
    layout: {
      margin: "0.75rem 0",
    },
  },
  bodyTextNormal: {
    style: {
      fontFamily: "'DM Sans', sans-serif",
      fontWeight: 400,
      fontSize: "0.875rem",   // 14px
      lineHeight: "100%",     // 14px
      letterSpacing: "0px",
      background: "#000000",
    },
    layout: {
      margin: "0.75rem 0",
    },
  },
  bodyTextSmall: {
    style: {
      fontFamily: "'DM Sans', sans-serif",
      fontWeight: 400,
      fontSize: "0.75rem",    // 12px
      lineHeight: "100%",     // 12px
      letterSpacing: "0px",
      background: "#000000",
    },
    layout: {
      margin: "0.5rem 0",
    },
  },
};

export type TypographyKey = keyof typeof typography;
