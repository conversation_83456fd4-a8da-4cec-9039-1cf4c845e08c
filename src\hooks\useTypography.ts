import { typography } from '../constants/typography';

/**
 * Hook to get typography styles for React components
 * Usage: const styles = useTypography('heading1');
 */
export const useTypography = (variant: keyof typeof typography) => {
  const typographyStyle = typography[variant];
  
  if (!typographyStyle) {
    console.warn(`Typography variant "${variant}" not found`);
    return typography.bodyTextNormal;
  }
  
  return typographyStyle;
};

/**
 * Get inline styles for a typography variant
 * Usage: const inlineStyles = getTypographyStyles('heading1');
 */
export const getTypographyStyles = (variant: keyof typeof typography) => {
  const typographyStyle = typography[variant];
  
  if (!typographyStyle) {
    console.warn(`Typography variant "${variant}" not found`);
    return typography.bodyTextNormal.style;
  }
  
  return typographyStyle.style;
};

/**
 * Get CSS class name for a typography variant
 * Usage: const className = getTypographyClassName('heading1');
 */
export const getTypographyClassName = (variant: keyof typeof typography): string => {
  const classNameMap = {
    heading1: 'heading1',
    heading2: 'heading2',
    bodyTextNormal: 'body-normal',
    bodyTextSmall: 'body-small',
  };
  
  return classNameMap[variant] || 'body-normal';
};

/**
 * Typography component props type
 */
export interface TypographyProps {
  variant?: keyof typeof typography;
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  as?: keyof JSX.IntrinsicElements;
}

/**
 * Get the appropriate HTML element for a typography variant
 */
export const getTypographyElement = (variant: keyof typeof typography): keyof JSX.IntrinsicElements => {
  const elementMap = {
    heading1: 'h1',
    heading2: 'h2',
    bodyTextNormal: 'p',
    bodyTextSmall: 'span',
  } as const;
  
  return elementMap[variant] || 'p';
};
