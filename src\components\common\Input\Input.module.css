.inputWrapper {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.label {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-gray-700);
}

.required {
  color: var(--color-red-500);
  margin-left: 2px;
}

.inputContainer {
  position: relative;
  display: flex;
  align-items: center;
}

.input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--color-gray-300);
  border-radius: 6px;
  font-size: 16px;
  line-height: 24px;
  background-color: var(--color-white);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-light);
}

.input:disabled {
  background-color: var(--color-gray-50);
  color: var(--color-gray-500);
  cursor: not-allowed;
}

.inputContainer.error .input {
  border-color: var(--color-red-500);
}

.inputContainer.error .input:focus {
  border-color: var(--color-red-500);
  box-shadow: 0 0 0 3px var(--color-red-light);
}

.icon {
  position: absolute;
  left: 12px;
  color: var(--color-gray-400);
  pointer-events: none;
}

.inputContainer .icon + .input {
  padding-left: 40px;
}

.errorMessage {
  font-size: 14px;
  color: var(--color-red-500);
}
