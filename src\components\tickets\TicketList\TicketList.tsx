import React from 'react';
import styles from './TicketList.module.css';
import { Table } from '../../common/Table/Table';
import { Pagination } from '../../common/Pagination/Pagination';
import { TextInput } from '../../common/Input/TextInput';
import { MagnifyingGlass } from '../../icons/MagnifyingGlass';
import { Funnel } from '../../icons/Funnel';

interface Ticket {
  id: string;
  subject: string;
  customer: string;
  status: 'open' | 'pending' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assignee: string;
  createdAt: string;
  updatedAt: string;
}

interface TicketListProps {
  tickets: Ticket[];
  selectedTicketId?: string;
  onTicketSelect: (ticket: Ticket) => void;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  loading?: boolean;
}

export const TicketList: React.FC<TicketListProps> = ({
  tickets,
  selectedTicketId,
  onTicketSelect,
  currentPage,
  totalPages,
  onPageChange,
  searchQuery,
  onSearchChange,
  loading = false,
}) => {
  const getStatusBadge = (status: Ticket['status']) => {
    const statusClasses = {
      open: styles.statusOpen,
      pending: styles.statusPending,
      resolved: styles.statusResolved,
      closed: styles.statusClosed,
    };

    return (
      <span className={`${styles.statusBadge} ${statusClasses[status]}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const getPriorityBadge = (priority: Ticket['priority']) => {
    const priorityClasses = {
      low: styles.priorityLow,
      medium: styles.priorityMedium,
      high: styles.priorityHigh,
      urgent: styles.priorityUrgent,
    };

    return (
      <span className={`${styles.priorityBadge} ${priorityClasses[priority]}`}>
        {priority.charAt(0).toUpperCase() + priority.slice(1)}
      </span>
    );
  };

  const columns = [
    {
      key: 'subject' as keyof Ticket,
      header: 'Subject',
      width: '30%',
    },
    {
      key: 'customer' as keyof Ticket,
      header: 'Customer',
      width: '20%',
    },
    {
      key: 'status' as keyof Ticket,
      header: 'Status',
      width: '15%',
      render: (value: Ticket['status']) => getStatusBadge(value),
    },
    {
      key: 'priority' as keyof Ticket,
      header: 'Priority',
      width: '15%',
      render: (value: Ticket['priority']) => getPriorityBadge(value),
    },
    {
      key: 'assignee' as keyof Ticket,
      header: 'Assignee',
      width: '20%',
    },
  ];

  return (
    <div className={styles.ticketList}>
      <div className={styles.header}>
        <h2 className={styles.title}>Tickets</h2>
        <div className={styles.actions}>
          <TextInput
            placeholder="Search tickets..."
            value={searchQuery}
            onChange={onSearchChange}
            icon={<MagnifyingGlass />}
          />
          <button className={styles.filterButton}>
            <Funnel />
            Filter
          </button>
        </div>
      </div>

      <div className={styles.tableContainer}>
        <Table
          data={tickets}
          columns={columns}
          onRowClick={onTicketSelect}
          selectedRowId={selectedTicketId}
          loading={loading}
        />
      </div>

      {totalPages > 1 && (
        <div className={styles.paginationContainer}>
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={onPageChange}
          />
        </div>
      )}
    </div>
  );
};
