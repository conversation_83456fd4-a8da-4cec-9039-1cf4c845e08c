import React, { useState, useRef, useCallback } from 'react';
import styles from './SplitPane.module.css';

interface SplitPaneProps {
  left: React.ReactNode;
  right: React.ReactNode;
  defaultSplit?: number; // Percentage (0-100)
  minSize?: number; // Minimum size in pixels
  maxSize?: number; // Maximum size in pixels
  split?: 'vertical' | 'horizontal';
}

export const SplitPane: React.FC<SplitPaneProps> = ({
  left,
  right,
  defaultSplit = 50,
  minSize = 200,
  maxSize = 800,
  split = 'vertical',
}) => {
  const [splitPosition, setSplitPosition] = useState(defaultSplit);
  const [isDragging, setIsDragging] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleMouseDown = useCallback(() => {
    setIsDragging(true);
  }, []);

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isDragging || !containerRef.current) return;

      const container = containerRef.current;
      const rect = container.getBoundingClientRect();
      
      let newPosition: number;
      
      if (split === 'vertical') {
        const x = e.clientX - rect.left;
        newPosition = (x / rect.width) * 100;
      } else {
        const y = e.clientY - rect.top;
        newPosition = (y / rect.height) * 100;
      }

      // Apply constraints
      const containerSize = split === 'vertical' ? rect.width : rect.height;
      const minPercent = (minSize / containerSize) * 100;
      const maxPercent = (maxSize / containerSize) * 100;
      
      newPosition = Math.max(minPercent, Math.min(maxPercent, newPosition));
      setSplitPosition(newPosition);
    },
    [isDragging, split, minSize, maxSize]
  );

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = split === 'vertical' ? 'col-resize' : 'row-resize';
      document.body.style.userSelect = 'none';
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };
  }, [isDragging, handleMouseMove, handleMouseUp, split]);

  const containerClass = split === 'vertical' ? styles.verticalContainer : styles.horizontalContainer;
  const resizerClass = split === 'vertical' ? styles.verticalResizer : styles.horizontalResizer;

  return (
    <div ref={containerRef} className={`${styles.splitPane} ${containerClass}`}>
      <div
        className={styles.pane}
        style={{
          [split === 'vertical' ? 'width' : 'height']: `${splitPosition}%`,
        }}
      >
        {left}
      </div>
      
      <div
        className={`${styles.resizer} ${resizerClass} ${isDragging ? styles.dragging : ''}`}
        onMouseDown={handleMouseDown}
      />
      
      <div
        className={styles.pane}
        style={{
          [split === 'vertical' ? 'width' : 'height']: `${100 - splitPosition}%`,
        }}
      >
        {right}
      </div>
    </div>
  );
};
