.ticketRow {
  padding: 16px;
  border-bottom: 1px solid var(--color-gray-100);
  cursor: pointer;
  transition: background-color 0.2s ease;
  background-color: var(--color-white);
}

.ticketRow:hover {
  background-color: var(--color-gray-50);
}

.ticketRow.selected {
  background-color: var(--color-primary-light);
  border-left: 4px solid var(--color-primary);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.subject {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-gray-900);
  line-height: 1.4;
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  margin-right: 12px;
}

.unreadBadge {
  background-color: var(--color-primary);
  color: var(--color-white);
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

.timestamp {
  font-size: 12px;
  color: var(--color-gray-500);
  white-space: nowrap;
}

.customer {
  font-size: 14px;
  color: var(--color-gray-700);
  margin-bottom: 12px;
}

.metadata {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.badges {
  display: flex;
  gap: 6px;
}

.statusBadge,
.priorityBadge {
  font-size: 11px;
  font-weight: 500;
  color: var(--color-white);
  padding: 2px 6px;
  border-radius: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.assignee {
  font-size: 12px;
  color: var(--color-gray-600);
}
