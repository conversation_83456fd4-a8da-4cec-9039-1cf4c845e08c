import React, { useState } from "react";
import { Header } from "./components/layout/Header/Header";
import { Sidebar } from "./components/layout/Sidebar/Sidebar";
import { SplitPane } from "./components/layout/SplitPane/SplitPane";
import { TicketList } from "./components/tickets/TicketList/TicketList";
import { Conversation } from "./components/tickets/ConversationPanel/Conversation";
import { ReplyForm } from "./components/tickets/ReplyForm/ReplyForm";
import { useFetchTickets } from "./hooks/useFetchTickets";
import { useFetchConversation } from "./hooks/useFetchConversation";
import { usePostMessage } from "./hooks/usePostMessage";
import { usePagination } from "./hooks/usePagination";
import { useTicketStore } from "./store/useTicketStore";
import "./styles/globals.css";

function App() {
  const [activeMenuItem, setActiveMenuItem] = useState("tickets");

  const { selectedTicket, searchQuery, setSelectedTicket, setSearchQuery } =
    useTicketStore();

  const { tickets, loading: ticketsLoading } = useFetchTickets();
  const { messages, loading: conversationLoading } = useFetchConversation(
    selectedTicket?.id || null
  );
  const { sendMessage, loading: sendingMessage } = usePostMessage();
  const { currentPage, totalPages, goToPage } = usePagination();

  const handleTicketSelect = (ticket: any) => {
    setSelectedTicket(ticket);
  };

  const handleSendMessage = async (content: string, attachments?: File[]) => {
    try {
      await sendMessage(content, attachments);
    } catch (error) {
      console.error("Failed to send message:", error);
    }
  };

  const renderMainContent = () => {
    if (activeMenuItem !== "tickets") {
      return (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            height: "100%",
            fontSize: "18px",
            color: "var(--color-gray-500)",
          }}
        >
          {activeMenuItem.charAt(0).toUpperCase() + activeMenuItem.slice(1)} -
          Coming Soon
        </div>
      );
    }

    const ticketListPanel = (
      <TicketList
        tickets={tickets}
        selectedTicketId={selectedTicket?.id}
        onTicketSelect={handleTicketSelect}
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={goToPage}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        loading={ticketsLoading}
      />
    );

    const conversationPanel = selectedTicket ? (
      <div style={{ display: "flex", flexDirection: "column", height: "100%" }}>
        <div style={{ flex: 1, overflow: "hidden" }}>
          <Conversation
            ticket={selectedTicket}
            messages={messages}
            loading={conversationLoading}
          />
        </div>
        <ReplyForm onSendMessage={handleSendMessage} loading={sendingMessage} />
      </div>
    ) : (
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          height: "100%",
          fontSize: "16px",
          color: "var(--color-gray-500)",
        }}
      >
        Select a ticket to view the conversation
      </div>
    );

    return (
      <SplitPane
        left={ticketListPanel}
        right={conversationPanel}
        defaultSplit={40}
        minSize={300}
        maxSize={600}
      />
    );
  };

  return (
    <div
      style={{
        display: "flex",
        height: "100vh",
        backgroundColor: "var(--color-bg-primary)",
      }}
    >
      <Sidebar activeItem={activeMenuItem} onItemClick={setActiveMenuItem} />

      <div style={{ flex: 1, display: "flex", flexDirection: "column" }}>
        <Header title="Customer Support Dashboard" />

        <main style={{ flex: 1, overflow: "hidden" }}>
          {renderMainContent()}
        </main>
      </div>
    </div>
  );
}

export default App;
