.messageWrapper {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.messageWrapper.agent {
  flex-direction: row-reverse;
}

.messageWrapper.customer {
  flex-direction: row;
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--color-gray-200);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-gray-600);
  overflow: hidden;
  flex-shrink: 0;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.messageContent {
  flex: 1;
  max-width: 70%;
}

.agent .messageContent {
  align-items: flex-end;
}

.customer .messageContent {
  align-items: flex-start;
}

.messageHeader {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.agent .messageHeader {
  justify-content: flex-end;
}

.customer .messageHeader {
  justify-content: flex-start;
}

.senderName {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-gray-900);
}

.timestamp {
  font-size: 12px;
  color: var(--color-gray-500);
}

.messageBubble {
  border-radius: 16px;
  padding: 12px 16px;
  word-wrap: break-word;
  position: relative;
}

.agentBubble {
  background-color: var(--color-primary);
  color: var(--color-white);
  border-bottom-right-radius: 4px;
}

.customerBubble {
  background-color: var(--color-gray-100);
  color: var(--color-gray-900);
  border-bottom-left-radius: 4px;
}

.messageText {
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
}

.attachments {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.agentBubble .attachments {
  border-top-color: rgba(255, 255, 255, 0.2);
}

.customerBubble .attachments {
  border-top-color: var(--color-gray-300);
}

.attachment {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.attachment:last-child {
  margin-bottom: 0;
}

.attachment svg {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.attachmentLink {
  font-size: 13px;
  text-decoration: underline;
  color: inherit;
}

.attachmentLink:hover {
  text-decoration: none;
}
