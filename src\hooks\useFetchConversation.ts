import { useEffect } from 'react';
import { useTicketStore, type Ticket } from '../store/useTicketStore';
import { ticketsApi } from '../services/ticketsApi';

interface UseFetchConversationOptions {
  autoFetch?: boolean;
  refetchInterval?: number;
}

export const useFetchConversation = (
  ticketId: string | null,
  options: UseFetchConversationOptions = {}
) => {
  const { autoFetch = true, refetchInterval } = options;
  
  const {
    messages,
    loading,
    error,
    setMessages,
    setLoading,
    setError,
  } = useTicketStore();

  const fetchConversation = async () => {
    if (!ticketId) {
      setMessages([]);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const response = await ticketsApi.getConversation(ticketId);
      setMessages(response.data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch conversation';
      setError(errorMessage);
      console.error('Error fetching conversation:', err);
    } finally {
      setLoading(false);
    }
  };

  const refetch = () => {
    fetchConversation();
  };

  useEffect(() => {
    if (autoFetch && ticketId) {
      fetchConversation();
    } else if (!ticketId) {
      setMessages([]);
    }
  }, [ticketId]);

  useEffect(() => {
    if (refetchInterval && refetchInterval > 0 && ticketId) {
      const interval = setInterval(fetchConversation, refetchInterval);
      return () => clearInterval(interval);
    }
  }, [refetchInterval, ticketId]);

  return {
    messages,
    loading,
    error,
    refetch,
  };
};
