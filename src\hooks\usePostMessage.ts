import { useState } from 'react';
import { useTicketStore } from '../store/useTicketStore';
import { ticketsApi } from '../services/ticketsApi';

export const usePostMessage = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { selectedTicket, addMessage } = useTicketStore();

  const sendMessage = async (content: string, attachments?: File[]) => {
    if (!selectedTicket) {
      throw new Error('No ticket selected');
    }

    try {
      setLoading(true);
      setError(null);

      // Upload attachments if any
      let uploadedAttachments: Array<{
        id: string;
        name: string;
        url: string;
        type: string;
      }> = [];

      if (attachments && attachments.length > 0) {
        const uploadPromises = attachments.map(file => 
          ticketsApi.uploadAttachment(file)
        );
        uploadedAttachments = await Promise.all(uploadPromises);
      }

      // Send the message
      const messageData = {
        content,
        attachments: uploadedAttachments,
      };

      const response = await ticketsApi.postMessage(selectedTicket.id, messageData);
      
      // Add the new message to the store
      addMessage(response.data);
      
      return response.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send message';
      setError(errorMessage);
      console.error('Error sending message:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const clearError = () => {
    setError(null);
  };

  return {
    sendMessage,
    loading,
    error,
    clearError,
  };
};
