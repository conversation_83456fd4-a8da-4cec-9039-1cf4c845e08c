// Placeholder for colors from Figma
// You will replace this content with the actual colors from your Figma file

export const colors = {
  // Primary colors
  primary: '#3B82F6',
  primaryLight: '#DBEAFE',
  primaryDark: '#1D4ED8',

  // Gray scale
  white: '#FFFFFF',
  gray50: '#F9FAFB',
  gray100: '#F3F4F6',
  gray200: '#E5E7EB',
  gray300: '#D1D5DB',
  gray400: '#9CA3AF',
  gray500: '#6B7280',
  gray600: '#4B5563',
  gray700: '#374151',
  gray800: '#1F2937',
  gray900: '#111827',

  // Status colors
  blue100: '#DBEAFE',
  blue500: '#3B82F6',
  blue800: '#1E40AF',
  
  green100: '#DCFCE7',
  green500: '#22C55E',
  green800: '#166534',
  
  yellow100: '#FEF3C7',
  yellow500: '#EAB308',
  yellow800: '#92400E',
  
  red100: '#FEE2E2',
  red500: '#EF4444',
  red700: '#B91C1C',
  
  orange100: '#FFEDD5',
  orange500: '#F97316',
  orange700: '#C2410C',
} as const;

export type ColorKey = keyof typeof colors;
