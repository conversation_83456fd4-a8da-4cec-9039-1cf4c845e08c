// Brand colors (consistent across themes)
const brandColors = {
  deepPurple: '#3A2F53',
  darkPurple: '#251F32',
  almostBlack: '#252526',
  grey: '#6C6C6C',
  purple: '#7336E6',
  lightPurple: '#D2C0F5',
  blue: '#2C9ED8',
  lightBlue: '#CAEDFF',
  greenYellow: '#BFD445',
  red: '#E76A63',
  paleRed: '#FDC2BF',
  lightGrey: '#F0F0F0',
  offWhite: '#F9F5FF',
};

// Theme-specific colors
export const lightTheme = {
  bgPrimary: brandColors.offWhite,
  bgSecondary: '#FFFFFF',
  bgTertiary: brandColors.lightGrey,
  textPrimary: brandColors.almostBlack,
  textSecondary: brandColors.grey,
  accentPrimary: brandColors.purple,
  accentSecondary: brandColors.lightPurple,
  statusOpen: brandColors.blue,
  statusPending: brandColors.greenYellow,
  statusClosed: brandColors.red,
  borderColor: brandColors.lightPurple,
  shadowPrimary: '0px 2px 6px rgba(226, 223, 233, 1)',
  shadowSecondary: '0px 2px 6px rgba(185, 181, 192, 0.65)',
};

export const darkTheme = {
  bgPrimary: brandColors.almostBlack,
  bgSecondary: brandColors.darkPurple,
  bgTertiary: '#333333',
  textPrimary: '#FFFFFF',
  textSecondary: '#B3B3B3',
  accentPrimary: brandColors.purple,
  accentSecondary: brandColors.deepPurple,
  statusOpen: brandColors.blue,
  statusPending: brandColors.greenYellow,
  statusClosed: brandColors.red,
  borderColor: '#444444',
  shadowPrimary: '0px 2px 6px rgba(0, 0, 0, 0.3)',
  shadowSecondary: '0px 2px 6px rgba(0, 0, 0, 0.5)',
};

// Export all colors for direct access
export const colors = {
  ...brandColors,
  light: lightTheme,
  dark: darkTheme,
};

export type ColorKey = keyof typeof colors;
export type ThemeColorKey = keyof typeof lightTheme;

export default colors;
