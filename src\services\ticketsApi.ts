import type { Ticket, Message } from '../store/useTicketStore';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';

interface ApiResponse<T> {
  data: T;
  message?: string;
}

interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    pageSize: number;
  };
}

interface GetTicketsParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  priority?: string;
}

interface PostMessageData {
  content: string;
  attachments?: Array<{
    id: string;
    name: string;
    url: string;
    type: string;
  }>;
}

class TicketsApiService {
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  async getTickets(params: GetTicketsParams = {}): Promise<PaginatedResponse<Ticket>> {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        searchParams.append(key, String(value));
      }
    });

    const queryString = searchParams.toString();
    const endpoint = `/tickets${queryString ? `?${queryString}` : ''}`;
    
    return this.request<PaginatedResponse<Ticket>>(endpoint);
  }

  async getTicket(ticketId: string): Promise<ApiResponse<Ticket>> {
    return this.request<ApiResponse<Ticket>>(`/tickets/${ticketId}`);
  }

  async getConversation(ticketId: string): Promise<ApiResponse<Message[]>> {
    return this.request<ApiResponse<Message[]>>(`/tickets/${ticketId}/messages`);
  }

  async postMessage(
    ticketId: string,
    messageData: PostMessageData
  ): Promise<ApiResponse<Message>> {
    return this.request<ApiResponse<Message>>(`/tickets/${ticketId}/messages`, {
      method: 'POST',
      body: JSON.stringify(messageData),
    });
  }

  async uploadAttachment(file: File): Promise<{
    id: string;
    name: string;
    url: string;
    type: string;
  }> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(`${API_BASE_URL}/attachments`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || 'Failed to upload attachment');
    }

    const result = await response.json();
    return result.data;
  }

  async updateTicketStatus(
    ticketId: string,
    status: Ticket['status']
  ): Promise<ApiResponse<Ticket>> {
    return this.request<ApiResponse<Ticket>>(`/tickets/${ticketId}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ status }),
    });
  }

  async updateTicketPriority(
    ticketId: string,
    priority: Ticket['priority']
  ): Promise<ApiResponse<Ticket>> {
    return this.request<ApiResponse<Ticket>>(`/tickets/${ticketId}/priority`, {
      method: 'PATCH',
      body: JSON.stringify({ priority }),
    });
  }

  async assignTicket(
    ticketId: string,
    assigneeId: string
  ): Promise<ApiResponse<Ticket>> {
    return this.request<ApiResponse<Ticket>>(`/tickets/${ticketId}/assign`, {
      method: 'PATCH',
      body: JSON.stringify({ assigneeId }),
    });
  }
}

export const ticketsApi = new TicketsApiService();
