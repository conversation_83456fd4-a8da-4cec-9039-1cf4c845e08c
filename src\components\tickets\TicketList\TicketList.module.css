.ticketList {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--color-white);
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  border-bottom: 1px solid var(--color-gray-200);
}

.title {
  font-size: 20px;
  font-weight: 600;
  color: var(--color-gray-900);
  margin: 0;
}

.actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filterButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid var(--color-gray-300);
  border-radius: 6px;
  background-color: var(--color-white);
  color: var(--color-gray-700);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filterButton:hover {
  background-color: var(--color-gray-50);
  border-color: var(--color-gray-400);
}

.tableContainer {
  flex: 1;
  overflow: hidden;
  padding: 0 24px;
}

.paginationContainer {
  padding: 0 24px 24px;
  border-top: 1px solid var(--color-gray-100);
}

/* Status badges */
.statusBadge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.statusOpen {
  background-color: var(--color-blue-100);
  color: var(--color-blue-800);
}

.statusPending {
  background-color: var(--color-yellow-100);
  color: var(--color-yellow-800);
}

.statusResolved {
  background-color: var(--color-green-100);
  color: var(--color-green-800);
}

.statusClosed {
  background-color: var(--color-gray-100);
  color: var(--color-gray-800);
}

/* Priority badges */
.priorityBadge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.priorityLow {
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
}

.priorityMedium {
  background-color: var(--color-blue-100);
  color: var(--color-blue-700);
}

.priorityHigh {
  background-color: var(--color-orange-100);
  color: var(--color-orange-700);
}

.priorityUrgent {
  background-color: var(--color-red-100);
  color: var(--color-red-700);
}
