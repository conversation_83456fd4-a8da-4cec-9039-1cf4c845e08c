.splitPane {
  display: flex;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.verticalContainer {
  flex-direction: row;
}

.horizontalContainer {
  flex-direction: column;
}

.pane {
  overflow: hidden;
  position: relative;
}

.resizer {
  background-color: var(--color-gray-200);
  transition: background-color 0.2s ease;
  position: relative;
  z-index: 1;
}

.resizer:hover,
.resizer.dragging {
  background-color: var(--color-primary);
}

.verticalResizer {
  width: 4px;
  cursor: col-resize;
  min-width: 4px;
  max-width: 4px;
}

.horizontalResizer {
  height: 4px;
  cursor: row-resize;
  min-height: 4px;
  max-height: 4px;
}

.resizer::before {
  content: '';
  position: absolute;
  background-color: transparent;
}

.verticalResizer::before {
  top: 0;
  bottom: 0;
  left: -2px;
  right: -2px;
}

.horizontalResizer::before {
  left: 0;
  right: 0;
  top: -2px;
  bottom: -2px;
}
