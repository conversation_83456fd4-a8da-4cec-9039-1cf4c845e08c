.pagination {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  padding: 16px 0;
}

.pageButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 1px solid var(--color-gray-300);
  border-radius: 6px;
  background-color: var(--color-white);
  color: var(--color-gray-700);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pageButton:hover:not(:disabled) {
  background-color: var(--color-gray-50);
  border-color: var(--color-gray-400);
}

.pageButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pageNumbers {
  display: flex;
  align-items: center;
  gap: 4px;
}

.pageNumber {
  min-width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--color-gray-300);
  border-radius: 6px;
  background-color: var(--color-white);
  color: var(--color-gray-700);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pageNumber:hover {
  background-color: var(--color-gray-50);
  border-color: var(--color-gray-400);
}

.pageNumber.active {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  color: var(--color-white);
}

.ellipsis {
  padding: 0 8px;
  color: var(--color-gray-500);
  font-size: 14px;
}
