.conversation {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--color-white);
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-color: var(--color-white);
}

.spinner {
  color: var(--color-gray-500);
  font-size: 16px;
}

.header {
  padding: 24px;
  border-bottom: 1px solid var(--color-gray-200);
  background-color: var(--color-gray-50);
}

.ticketInfo {
  margin-bottom: 20px;
}

.subject {
  font-size: 20px;
  font-weight: 600;
  color: var(--color-gray-900);
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.metadata {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.status,
.priority {
  font-size: 11px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status.open { background-color: var(--color-blue-500); color: white; }
.status.pending { background-color: var(--color-yellow-500); color: white; }
.status.resolved { background-color: var(--color-green-500); color: white; }
.status.closed { background-color: var(--color-gray-500); color: white; }

.priority.low { background-color: var(--color-gray-400); color: white; }
.priority.medium { background-color: var(--color-blue-500); color: white; }
.priority.high { background-color: var(--color-orange-500); color: white; }
.priority.urgent { background-color: var(--color-red-500); color: white; }

.ticketId {
  font-size: 12px;
  color: var(--color-gray-600);
  font-family: monospace;
}

.customerInfo {
  border-top: 1px solid var(--color-gray-200);
  padding-top: 20px;
}

.customerHeader {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--color-gray-200);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-gray-600);
  overflow: hidden;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.customerDetails {
  flex: 1;
}

.customerName {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-gray-900);
  margin: 0 0 8px 0;
}

.contactInfo {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.contactItem {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--color-gray-600);
}

.contactItem svg {
  width: 16px;
  height: 16px;
  color: var(--color-gray-400);
}

.messagesContainer {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}
