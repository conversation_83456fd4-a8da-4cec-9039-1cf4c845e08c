import React from 'react';
import styles from './TicketRow.module.css';

interface Ticket {
  id: string;
  subject: string;
  customer: string;
  status: 'open' | 'pending' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assignee: string;
  createdAt: string;
  updatedAt: string;
  unreadCount?: number;
}

interface TicketRowProps {
  ticket: Ticket;
  isSelected: boolean;
  onClick: () => void;
}

export const TicketRow: React.FC<TicketRowProps> = ({
  ticket,
  isSelected,
  onClick,
}) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const getStatusColor = (status: Ticket['status']) => {
    const colors = {
      open: '#3B82F6',
      pending: '#F59E0B',
      resolved: '#10B981',
      closed: '#6B7280',
    };
    return colors[status];
  };

  const getPriorityColor = (priority: Ticket['priority']) => {
    const colors = {
      low: '#6B7280',
      medium: '#3B82F6',
      high: '#F97316',
      urgent: '#EF4444',
    };
    return colors[priority];
  };

  return (
    <div
      className={`${styles.ticketRow} ${isSelected ? styles.selected : ''}`}
      onClick={onClick}
    >
      <div className={styles.header}>
        <div className={styles.subject}>
          {ticket.subject}
          {ticket.unreadCount && ticket.unreadCount > 0 && (
            <span className={styles.unreadBadge}>{ticket.unreadCount}</span>
          )}
        </div>
        <div className={styles.timestamp}>
          {formatDate(ticket.updatedAt)}
        </div>
      </div>

      <div className={styles.customer}>
        {ticket.customer}
      </div>

      <div className={styles.metadata}>
        <div className={styles.badges}>
          <span
            className={styles.statusBadge}
            style={{ backgroundColor: getStatusColor(ticket.status) }}
          >
            {ticket.status}
          </span>
          <span
            className={styles.priorityBadge}
            style={{ backgroundColor: getPriorityColor(ticket.priority) }}
          >
            {ticket.priority}
          </span>
        </div>
        <div className={styles.assignee}>
          {ticket.assignee}
        </div>
      </div>
    </div>
  );
};
