import React from 'react';
import styles from './MessageBubble.module.css';
import { PaperClip } from '../../icons/PaperClip';
import { User } from '../../icons/User';

interface Message {
  id: string;
  content: string;
  sender: {
    name: string;
    type: 'customer' | 'agent';
    avatar?: string;
  };
  timestamp: string;
  attachments?: Array<{
    id: string;
    name: string;
    url: string;
    type: string;
  }>;
}

interface MessageBubbleProps {
  message: Message;
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({ message }) => {
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString([], {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const isAgent = message.sender.type === 'agent';

  return (
    <div className={`${styles.messageWrapper} ${isAgent ? styles.agent : styles.customer}`}>
      <div className={styles.avatar}>
        {message.sender.avatar ? (
          <img src={message.sender.avatar} alt={message.sender.name} />
        ) : (
          <User />
        )}
      </div>
      
      <div className={styles.messageContent}>
        <div className={styles.messageHeader}>
          <span className={styles.senderName}>{message.sender.name}</span>
          <span className={styles.timestamp}>{formatTimestamp(message.timestamp)}</span>
        </div>
        
        <div className={`${styles.messageBubble} ${isAgent ? styles.agentBubble : styles.customerBubble}`}>
          <div className={styles.messageText}>
            {message.content}
          </div>
          
          {message.attachments && message.attachments.length > 0 && (
            <div className={styles.attachments}>
              {message.attachments.map((attachment) => (
                <div key={attachment.id} className={styles.attachment}>
                  <PaperClip />
                  <a
                    href={attachment.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={styles.attachmentLink}
                  >
                    {attachment.name}
                  </a>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
