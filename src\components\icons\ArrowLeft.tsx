import React from 'react';

interface IconProps {
  width?: string | number;
  height?: string | number;
  className?: string;
}

export const ArrowLeft: React.FC<IconProps> = ({ 
  width = 20, 
  height = 20, 
  className = '' 
}) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={2}
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
  >
    <path d="m12 19-7-7 7-7" />
    <path d="M19 12H5" />
  </svg>
);
