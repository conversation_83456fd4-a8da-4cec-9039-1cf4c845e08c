.tableContainer {
  overflow-x: auto;
  border: 1px solid var(--color-gray-200);
  border-radius: 8px;
  background-color: var(--color-white);
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.thead {
  background-color: var(--color-gray-50);
}

.th {
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  font-size: 14px;
  color: var(--color-gray-700);
  border-bottom: 1px solid var(--color-gray-200);
}

.tbody {
  background-color: var(--color-white);
}

.tr {
  border-bottom: 1px solid var(--color-gray-100);
  transition: background-color 0.2s ease;
}

.tr:last-child {
  border-bottom: none;
}

.tr.clickable {
  cursor: pointer;
}

.tr.clickable:hover {
  background-color: var(--color-gray-50);
}

.tr.selected {
  background-color: var(--color-primary-light);
}

.td {
  padding: 12px 16px;
  font-size: 14px;
  color: var(--color-gray-900);
  vertical-align: top;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  background-color: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: 8px;
}

.spinner {
  color: var(--color-gray-500);
  font-size: 16px;
}
